<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VxeTable 自适应分页测试</title>
</head>
<body>
    <h1>VxeTable 自适应分页修复测试</h1>
    
    <h2>修复说明</h2>
    <p>修复了当表格自适应计算出的页面大小与固定选项相同时，两个选项都被选中的问题。</p>
    
    <h3>修改内容：</h3>
    <ol>
        <li><strong>自适应选项使用特殊标识符：</strong>将自适应选项的value从动态数字改为固定的'adaptive'字符串</li>
        <li><strong>更新标签显示：</strong>自适应选项的标签显示为"自适应(X条)"，清楚显示当前计算的条数</li>
        <li><strong>添加计算属性：</strong>添加currentPageSize计算属性来处理VxePager的双向绑定</li>
        <li><strong>修复页面变化处理：</strong>更新onPageChange方法来正确处理自适应选项的选择</li>
        <li><strong>统一页面大小计算：</strong>在序号配置和分页信息显示中统一使用正确的页面大小</li>
    </ol>
    
    <h3>测试场景：</h3>
    <ul>
        <li>当自适应计算出20条时，不会与"20条/页"选项冲突</li>
        <li>自适应选项显示为"自适应(20条)"，固定选项显示为"20条/页"</li>
        <li>选择自适应时，实际使用计算出的条数进行分页</li>
        <li>选择固定选项时，使用固定的条数进行分页</li>
    </ul>
    
    <h3>核心修改点：</h3>
    <pre><code>
// 1. 自适应选项使用特殊value
{ label: '自适应', value: 'adaptive' }

// 2. dealSize方法中更新标签但保持value不变
this.pageSize = this.pageSize.map((el, i) => 
  (i === 0 ? { label: `自适应(${this.adaptPageSize}条)`, value: 'adaptive' } : el)
)

// 3. onPageChange方法中检查自适应选项
if (page.type === 'size' && (obj.currentOption?.value === 'adaptive' || page.pageSize === 'adaptive')) {
  // 处理自适应逻辑
}

// 4. 添加计算属性处理双向绑定
currentPageSize: {
  get() {
    return this.isAdaptPageSizeData ? 'adaptive' : this.tablePage.pageSize
  },
  set(value) {
    if (value !== 'adaptive') {
      this.tablePage.pageSize = value
    }
  }
}
    </code></pre>
    
    <p><strong>结果：</strong>现在自适应选项和固定选项不会再出现value冲突的问题，每个选项都有唯一的标识符。</p>

    <h3>修改文件：</h3>
    <p><code>src/components/VxeTable/index.vue</code></p>

    <h3>验证方法：</h3>
    <ol>
        <li>启用自适应分页功能（isAdaptPageSize: true）</li>
        <li>调整浏览器窗口大小，使自适应计算出20条数据</li>
        <li>查看分页器下拉选项，应该看到：
            <ul>
                <li>"自适应(20条)" - value为'adaptive'</li>
                <li>"20条/页" - value为20</li>
            </ul>
        </li>
        <li>两个选项不会同时被选中</li>
        <li>选择任一选项都能正常工作</li>
    </ol>
</body>
</html>
